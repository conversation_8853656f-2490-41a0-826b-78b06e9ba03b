import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Block, BlockNoteEditor, PartialBlock } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  BasicTextStyleButton,
  BlockTypeSelect,
  ColorStyleButton,
  CreateLinkButton,
  FormattingToolbar,
  FormattingToolbarController,
  NestBlockButton,
  TextAlignButton,
  UnnestBlockButton,
  // filterSuggestionItems, // Commented out to debug import error
  DefaultReactSuggestionItem,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
} from '@blocknote/react';
import { BookmarkPlus, PlusSquare, Save } from 'lucide-react'; // or any preferred icon
import { useSavedParagraphsStore } from '../../../stores/savedParagraphsStore';
import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';
import removeMarkdown from 'remove-markdown';
import { useTheme } from './../../../hooks/useTheme';
import { useDocumentStore, useActiveDocument, useActiveFileId, useEditorMode, useProjectSettings, usePendingInsertionContent, usePendingInsertionActions } from './../../../stores/blockDocumentStore';
import { blockAutoSaveManager } from './../../../services/BlockAutoSaveManager';
import { Skeleton } from './../../ui/skeleton';
import { FileWarning } from 'lucide-react';
import { EditorToolbar } from './../EditorToolbar';
import { useNotification } from './../../../contexts/NotificationContext';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../ui/tooltip';

// Import constants
import { COPY_CONSTANTS } from './constants';

// Import utilities
import {
  getSelectionState,
  generateHtmlFromSelection,
  generateContentFromBlocks,
  applySystemPromptPrepend,
  applySystemPromptPrependToHtml
} from './utils/copyUtils';

import {
  extractHyperlinksFromBlocks,
  extractHyperlinksFromHtml,
  textContainsCaseInsensitive,
  restoreHyperlinksInSpecificBlocks
} from './utils/hyperlinkUtils';

import { detectAffectedBlocks } from './utils/blockUtils';

// Import components
import { RestyleButton } from './components/RestyleButton';

// Types for editor modes
export type EditorMode = 'edit' | 'preview' | 'split';

// Types for copy functionality
interface SelectionState {
  browserSelection: Selection | null;
  rawSelectedText: string;
  hasActiveVisualSelection: boolean;
}

interface CopyContent {
  plainText: string;
  html: string;
}

interface CopyError extends Error {
  code: 'SELECTION_ERROR' | 'CLIPBOARD_ERROR' | 'CONTENT_GENERATION_ERROR';
  originalError?: Error;
}

// Types for simplified hyperlink saving functionality
interface SimplifiedHyperlinkDictionary {
  [normalizedText: string]: string; // text -> URL mapping
}

const createSavedParagraphsItem = (editor: BlockNoteEditor): DefaultReactSuggestionItem => ({
  title: "Saved paragraphs",
  onItemClick: () => {
    console.log('Saved paragraphs clicked');
  },
  aliases: ["saved", "paragraphs", "sp"],
  group: "Saved",
  icon: <BookmarkPlus size={18} />,
  subtext: "Access your saved paragraph templates",
});

const getCustomSlashMenuItems = (editor: BlockNoteEditor): DefaultReactSuggestionItem[] => {
  const defaultItems = getDefaultReactSlashMenuItems(editor);

  const filteredDefaultItems = defaultItems.filter(
    (item) =>
      !["Video", "File", "Audio", "Emoji", "Image"].includes(item.title)
  );
  return [
    createSavedParagraphsItem(editor),
    ...filteredDefaultItems,
  ];
};

/**
 * EditorView Component
 * Renders a BlockNote editor with background auto-save functionality and optimized performance
 */
const EditorView: React.FC = () => {
  // Store selectors for optimized rendering
  const activeFile = useActiveDocument();
  const activeFileId = useActiveFileId();
  const mode = useEditorMode();
  const projectSettings = useProjectSettings();

  // Store methods
  const { setEditorMode } = useDocumentStore();

  // Block document store access
  const { updateBlock, markBlockDirty, deleteBlock } = useDocumentStore();

  // Pending insertion content hooks
  const pendingInsertionContent = usePendingInsertionContent();
  const { setPendingInsertionContent } = usePendingInsertionActions();

  // Saved paragraphs store
  const { setInitialCreateContent, setCurrentView, openModal } = useSavedParagraphsStore();

  // Context hooks
  const { theme } = useTheme();
  const { showNotification } = useNotification();

  // Map theme to BlockNote theme prop
  const getBlockNoteTheme = useCallback(() => {
    switch (theme) {
      case 'light':
        return 'light';
      case 'dark':
        return 'dark';
      case 'custom':
        // Use dark as base theme, CSS overrides will handle custom styling
        return 'dark';
      default:
        return 'light';
    }
  }, [theme]);

  // Auto-save will be handled directly by the store and BlockAutoSaveManager

  // Get save status and dirty blocks from the store
  const isAutoSaving = useDocumentStore(state => state.isAutoSaving);
  const dirtyBlocks = useDocumentStore(state => state.dirtyBlocks);
  const hasUnsavedChanges = dirtyBlocks.size > 0;
  const saveStatus: 'idle' | 'saving' | 'saved' | 'error' = isAutoSaving ? 'saving' : 'idle';

  // Get blocks for the current document directly from the store
  const documentBlocks = useDocumentStore(state =>
    activeFileId ? state.getDocumentBlocks(activeFileId) : []
  );

  // Refs for editor management
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<BlockNoteEditor | null>(null);
  const isUpdatingProgrammatically = useRef<boolean>(false);
  const lastLoadedBlocksRef = useRef<any[]>([]);

  // Ref for simplified hyperlink dictionary storage
  const hyperlinkDictionaryRef = useRef<SimplifiedHyperlinkDictionary>({});

  // Editor container styles with CSS containment for proper isolation
  const editorContainerStyles: React.CSSProperties = {
    contain: 'layout style paint',
    isolation: 'isolate',
    position: 'relative',
    overflow: 'hidden'
  };

  // Load blocks for the current document when it changes
  useEffect(() => {
    if (activeFileId) {
      console.log(`[EditorView] Loading blocks for document: ${activeFileId}`);

      // Reset the last loaded blocks reference when switching documents
      lastLoadedBlocksRef.current = [];
      isUpdatingProgrammatically.current = false;

      const store = useDocumentStore.getState();

      // Check if document is block-based and load blocks
      store.isDocumentBlockBased(activeFileId).then(isBlockBased => {
        if (isBlockBased) {
          store.loadDocumentBlocks(activeFileId);
        }
      });
    }
  }, [activeFileId]);

  // Create the editor instance (only when activeFileId changes)
  const editor = useMemo(() => {
    if (!activeFileId) {
      editorRef.current = null;
      return undefined;
    }

    console.log("[EditorView] Creating BlockNoteEditor instance for document:", activeFileId);

    // Create editor with custom paste handler for hyperlink restoration
    const instance = BlockNoteEditor.create({
      initialContent: [{ type: 'paragraph', content: [] }],
      pasteHandler: ({ event, editor, defaultPasteHandler }) => {
        console.log('[EditorView] 🔄 Custom paste handler triggered');

        // Check if hyperlink saving is enabled
        const state = useDocumentStore.getState();
        const activeFile = state.detailedActiveFile;
        const isHyperlinkSavingEnabled = activeFile?.documentSettings?.hyperlinkSavingEnabled ?? false;

        // Check if clipboard has rich text (HTML) data - indicates copy from BlockNote
        const hasHtmlData = event.clipboardData?.types.includes('text/html');
        const pastedText = event.clipboardData?.getData('text/plain');
        const pastedHtml = event.clipboardData?.getData('text/html');

        console.log('[EditorView] 📋 PASTE ANALYSIS:');
        console.log('  🔗 Hyperlink saving enabled:', isHyperlinkSavingEnabled);
        console.log('  📄 Has HTML data:', hasHtmlData);
        console.log('  📝 Has text data:', !!pastedText);
        console.log('  🎨 HTML content preview:', pastedHtml?.substring(0, 200) + '...');

        // If hyperlink saving is disabled, use default paste without hyperlink restoration
        if (!isHyperlinkSavingEnabled) {
          console.log('[EditorView] 📋 Hyperlink saving disabled, using default paste (preserving existing hyperlinks in content)');
          return defaultPasteHandler();
        }

        // If no text data, use default paste
        if (!pastedText) {
          console.log('[EditorView] 📋 No text data in clipboard, using default paste');
          return defaultPasteHandler();
        }

        // Detect if content is primarily markdown (has markdown syntax) - do this FIRST
        const isMarkdownContent = /(?:^|\n)#{1,6}\s|(?:^|\n)[-*+]\s|\*\*.*\*\*|\*.*\*|```|---|\[.*\]\(.*\)/.test(pastedText);
        console.log('[EditorView] 📝 Markdown detection:', isMarkdownContent);

        if (isMarkdownContent) {
          console.log('[EditorView] 🔄 Using BlockNote markdown parsing for better line break handling');

          // Prepare markdown text for parsing - preserve line breaks but clean up Windows line endings
          let markdownText = pastedText
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n');

          // Convert newlines for better paragraph separation:
          // - Single newlines (\n) → Each line becomes its own paragraph
          // - Double newlines (\n\n) → Empty line between paragraphs
          markdownText = markdownText
            .split('\n\n') // Split on existing paragraph breaks
            .map(section => {
              // Within each section, convert single newlines to double newlines
              // so each line becomes its own paragraph
              return section
                .split('\n')
                .filter(line => line.trim().length > 0) // Remove empty lines
                .join('\n\n'); // Each line becomes its own paragraph
            })
            .filter(section => section.trim().length > 0) // Remove empty sections
            .join('\n\n\n\n'); // Add empty paragraph between sections (double newlines become empty line)

          console.log('[EditorView] 📝 Markdown text for parsing:', JSON.stringify(markdownText));

          // Use BlockNote's built-in markdown paste method for proper handling
          console.log('[EditorView] 🔄 Using editor.pasteMarkdown() for proper paste handling');
          let prePasteBlocksMarkdown: Block[] = [];
          console.log('[EditorView] 📸 Capturing pre-markdown-paste block state...');
          prePasteBlocksMarkdown = [...editor.document]; // Snapshot current blocks
          editor.pasteMarkdown(markdownText);

          // Check if we need hyperlink restoration after markdown parsing
          const normalizedText = pastedText
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/\n\s*\n/g, '\n')
            .trim();

          const hasMatchingHyperlinks = Object.keys(hyperlinkDictionaryRef.current).some(dictText =>
            textContainsCaseInsensitive(normalizedText, dictText)
          );

          if (hasMatchingHyperlinks) {
            console.log('[EditorView] 🔗 Applying hyperlink restoration to markdown content');
            // Apply hyperlink restoration after markdown parsing
            setTimeout(() => {
              try {
                // Get post-paste blocks
                const postPasteBlocksMarkdown = editor.document;
                console.log('[EditorView] 📊 Markdown Block count change:', prePasteBlocksMarkdown.length, '→', postPasteBlocksMarkdown.length);

                // Find blocks that were added or modified
                const affectedBlocks = detectAffectedBlocks(prePasteBlocksMarkdown, postPasteBlocksMarkdown, normalizedText, editor);

                console.log('[EditorView] 🎯 Detected affected blocks for markdown:', affectedBlocks.map(b => `${b.id.substring(0, 8)}...`));

                if (affectedBlocks && affectedBlocks.length > 0) {
                  console.log('[EditorView] 🔗 Calling restoreHyperlinksInSpecificBlocks for markdown content');
                  // `normalizedText` (from line 508-512) is the full pasted text.
                  restoreHyperlinksInSpecificBlocks(affectedBlocks, normalizedText, hyperlinkDictionaryRef.current, editor);
                } else {
                  console.warn('[EditorView] ⚠️ Could not determine affected blocks for markdown hyperlink restoration. Hyperlinks may not be restored.');
                }
                console.log('[EditorView] ✅ Hyperlink restoration attempt completed for markdown.');
              } catch (error) {
                console.error('[EditorView] ❌ Error during hyperlink restoration for markdown:', error);
              }
            }, 100);
          } else {
            console.log('[EditorView] 📝 Markdown pasted without hyperlinks to restore');
          }

          // Return early to prevent default paste
          return;
        }

        // If not markdown, handle as rich text with potential hyperlink restoration
        if (hasHtmlData) {
          // Normalize pasted text for dictionary lookup
          const normalizedText = pastedText
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/\n\s*\n/g, '\n')
            .trim();

          // Check for matching hyperlinks in dictionary
          const hasMatchingHyperlinks = Object.keys(hyperlinkDictionaryRef.current).some(dictText =>
            textContainsCaseInsensitive(normalizedText, dictText)
          );

          if (!hasMatchingHyperlinks) {
            console.log('[EditorView] 🎨 Rich text paste with no hyperlink restoration needed, using default paste');
            return defaultPasteHandler();
          }

          console.log('[EditorView] 🔗 Rich text paste with hyperlink restoration needed, proceeding with custom handling');
        }

        // Normalize pasted text for dictionary lookup
        const normalizedText = pastedText
          .replace(/\r\n/g, '\n')
          .replace(/\r/g, '\n')
          .replace(/\n\s*\n/g, '\n')
          .trim();

        console.log('[EditorView] 📋 PASTE OPERATION:');
        console.log('  📝 Raw pasted text:', JSON.stringify(pastedText));
        console.log('  📝 Normalized text:', JSON.stringify(normalizedText));
        console.log('  📚 Dictionary state:', JSON.stringify(hyperlinkDictionaryRef.current, null, 2));

        // Check for matching hyperlinks in dictionary
        const matchingUrls: string[] = [];
        Object.keys(hyperlinkDictionaryRef.current).forEach(dictText => {
          if (textContainsCaseInsensitive(normalizedText, dictText)) {
            matchingUrls.push(hyperlinkDictionaryRef.current[dictText]);
            console.log('[EditorView] 🔗 Found matching text:', {
              dictText: JSON.stringify(dictText),
              url: hyperlinkDictionaryRef.current[dictText]
            });
          }
        });

        if (matchingUrls.length === 0) {
          console.log('[EditorView] ❌ No matching hyperlinks found, using default paste');
          return defaultPasteHandler();
        }

        console.log('[EditorView] ✅ Found matching hyperlinks, applying custom paste with links');

        // Use default paste first, then add hyperlinks
        let prePasteBlocksRichText: Block[] = [];
        console.log('[EditorView] 📸 Capturing pre-rich-text-paste block state...');
        prePasteBlocksRichText = [...editor.document]; // Snapshot current blocks
        const result = defaultPasteHandler();

        // Add hyperlinks after paste using setTimeout to ensure paste is complete
        setTimeout(() => {
          try {
            console.log('[EditorView] 🔗 Starting hyperlink restoration process...');
            // Get blocks affected by the paste operation
            // Get post-paste blocks
            const postPasteBlocksRichText = editor.document;
            console.log('[EditorView] 📊 Rich Text/Plain Block count change:', prePasteBlocksRichText.length, '→', postPasteBlocksRichText.length);

            // Find blocks that were added or modified
            const affectedBlocks = detectAffectedBlocks(prePasteBlocksRichText, postPasteBlocksRichText, normalizedText, editor);

            console.log('[EditorView] 🎯 Detected affected blocks for rich text/plain:', affectedBlocks.map(b => `${b.id.substring(0, 8)}...`));

            if (affectedBlocks && affectedBlocks.length > 0) {
              console.log('[EditorView] 🔗 Pasted text contains potential dictionary keys. Attempting restoration in affected blocks for rich text.');
              restoreHyperlinksInSpecificBlocks(affectedBlocks, normalizedText, hyperlinkDictionaryRef.current, editor);
            } else {
              console.warn('[EditorView] ⚠️ Could not determine affected blocks from selection/cursor post-paste for rich text hyperlink restoration.');
            }

            console.log('[EditorView] ✅ Hyperlink restoration completed');
          } catch (error) {
            console.error('[EditorView] ❌ Error during hyperlink restoration:', error);
          }
        }, 100);

        return result;
      }
    });

    editorRef.current = instance;
    return instance;
  }, [activeFileId]); // Only recreate when activeFileId changes

  // Helper function to compare block arrays for content equality
  const blocksAreEqual = useCallback((blocks1: any[], blocks2: any[]): boolean => {
    if (blocks1.length !== blocks2.length) return false;

    return blocks1.every((block1, index) => {
      const block2 = blocks2[index];
      // Compare core block properties, ignoring documentId which is metadata
      return block1?.id === block2?.id &&
             JSON.stringify(block1?.content) === JSON.stringify(block2?.content) &&
             block1?.type === block2?.type;
    });
  }, []);

  // Helper function to get the current system prompt content
  const getCurrentSystemPrompt = useCallback((): string => {
    console.log('[EditorView] getCurrentSystemPrompt called');
    console.log('[EditorView] activeFile?.documentSettings:', activeFile?.documentSettings);
    console.log('[EditorView] projectSettings:', projectSettings);

    if (!activeFile?.documentSettings) {
      console.log('[EditorView] Missing activeFile.documentSettings');
      return '';
    }

    const documentSettings = activeFile.documentSettings;
    console.log('[EditorView] documentSettings:', documentSettings);
    console.log('[EditorView] selectedSystemPromptId:', documentSettings.selectedSystemPromptId);

    // First check for document-specific override
    if (documentSettings.systemPromptContentOverride !== undefined && documentSettings.systemPromptContentOverride !== '') {
      console.log('[EditorView] Using systemPromptContentOverride:', documentSettings.systemPromptContentOverride);
      return documentSettings.systemPromptContentOverride;
    }

    // Then check for selected prompt (could be global or local)
    if (documentSettings.selectedSystemPromptId) {
      const selectedId = documentSettings.selectedSystemPromptId;
      console.log('[EditorView] Looking for prompt with ID:', selectedId);

      // First check local/document prompts
      if (documentSettings.localSystemPrompts && documentSettings.localSystemPrompts.length > 0) {
        console.log('[EditorView] Checking local prompts:', documentSettings.localSystemPrompts);
        const localPrompt = documentSettings.localSystemPrompts.find(
          (p: any) => p.id === selectedId
        );
        if (localPrompt) {
          console.log('[EditorView] Found LOCAL prompt:', localPrompt);
          return localPrompt.content || '';
        }
      }

      // Then check global prompts
      if (projectSettings && projectSettings.systemPrompts) {
        console.log('[EditorView] Checking global prompts:', projectSettings.systemPrompts);
        const globalPrompt = projectSettings.systemPrompts.find(
          (p: any) => p.id === selectedId
        );
        if (globalPrompt) {
          console.log('[EditorView] Found GLOBAL prompt:', globalPrompt);
          return globalPrompt.content || '';
        }
      }

      console.log('[EditorView] Prompt ID found but no matching prompt content');
    }

    console.log('[EditorView] No prompt found, returning empty string');
    return '';
  }, [activeFile?.documentSettings, projectSettings]);

  // Update editor content when document blocks are loaded
  useEffect(() => {
    if (!editor || !activeFileId) return;

    // Skip if we're in the middle of a programmatic update (like during save)
    if (isUpdatingProgrammatically.current) {
      console.log("[EditorView] Skipping editor content update - programmatic update in progress");
      return;
    }

    try {
      // Get blockOrder from metadata to ensure correct sequencing
      const store = useDocumentStore.getState();
      const metadata = store.documentsMetadata.get(activeFileId);
      const blockOrder = metadata?.blockOrder || [];

      // Prepare blocks in the correct order using blockOrder from metadata
      let orderedBlocks: any[] = [];
      if (blockOrder.length > 0 && documentBlocks.length > 0) {
        // Create a map for quick block lookup
        const blockMap = new Map(documentBlocks.map(block => [block.content.id, block.content]));

        // Build ordered array using blockOrder sequence
        orderedBlocks = blockOrder
          .map(blockId => blockMap.get(blockId))
          .filter(block => block !== undefined); // Remove any missing blocks
      } else if (documentBlocks.length > 0) {
        // Fallback to document blocks order if no metadata blockOrder
        orderedBlocks = documentBlocks.map(block => block.content);
        console.log("[EditorView] Using fallback block order (no metadata):", orderedBlocks.length);
      }

      // Check if the content has actually changed to prevent unnecessary updates
      if (blocksAreEqual(orderedBlocks, lastLoadedBlocksRef.current)) {
        return;
      }

      // Set flag to prevent onChange handler from firing during programmatic update
      isUpdatingProgrammatically.current = true;

      // Store the current blocks for comparison
      lastLoadedBlocksRef.current = orderedBlocks;

      if (orderedBlocks.length > 0) {
        console.log("[EditorView] Replacing editor content with correctly ordered blocks:", orderedBlocks);

        // Replace editor content with correctly ordered blocks
        editor.replaceBlocks(editor.document, orderedBlocks);
      } else {
        // If no blocks are loaded yet, ensure editor has at least one empty paragraph
        const currentBlocks = editor.document;
        if (currentBlocks.length === 0) {
          console.log("[EditorView] No blocks loaded and editor is empty, replacing with default paragraph");
          const defaultBlock = [{ type: 'paragraph' as const, content: [] }];
          editor.replaceBlocks(editor.document, defaultBlock);
          lastLoadedBlocksRef.current = defaultBlock;
        }
      }

      // Use a more reliable method to reset the flag
      // Instead of setTimeout, use requestAnimationFrame to ensure the editor has processed the change
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          isUpdatingProgrammatically.current = false;
          console.log("[EditorView] Programmatic update flag reset");
        });
      });
    } catch (error) {
      console.error("[EditorView] Error updating editor content:", error);
      // Reset flag on error as well
      isUpdatingProgrammatically.current = false;
    }
  }, [editor, activeFileId, documentBlocks, blocksAreEqual]);

  // Handle pending insertion content from saved paragraphs
  useEffect(() => {
    // Only run when there's actually pending content to insert
    if (!pendingInsertionContent) return;

    // Ensure editor is available at runtime
    if (!editor) {
      console.warn('[EditorView] ⚠️ Editor not available for content insertion, clearing pending content');
      setPendingInsertionContent(null);
      return;
    }

    try {
      console.log('[EditorView] 🚀 Inserting saved paragraph content:', pendingInsertionContent);

      // Get the current cursor position
      const textCursorPosition = editor.getTextCursorPosition();
      const currentBlock = textCursorPosition.block;

      console.log('[EditorView] 📍 Current block for insertion:', currentBlock.id);

      // Insert the blocks after the current block
      editor.insertBlocks(pendingInsertionContent, currentBlock, 'after');

      console.log('[EditorView] ✅ Successfully inserted saved paragraph blocks');

      // Clear the pending content
      setPendingInsertionContent(null);
    } catch (error) {
      console.error('[EditorView] ❌ Error inserting saved paragraph content:', error);
      // Clear pending content even on error to prevent retry loops
      setPendingInsertionContent(null);
    }
  }, [pendingInsertionContent, setPendingInsertionContent]);

  // Content change handler with block-based auto-save integration
  const handleEditorChange = useCallback(() => {
    if (!editor || !activeFileId) return;

    // Skip if we're updating programmatically to prevent infinite loops
    if (isUpdatingProgrammatically.current) {
      console.log("[EditorView] Skipping change handler - programmatic update in progress");
      return;
    }

    console.log("[EditorView] Processing user-initiated change");
    const currentBlocks = editor.document;
    console.log("[EditorView] Current blocks count:", currentBlocks.length);

    // Capture authoritative blockOrder from editor - this is the new definitive order
    const newBlockOrder = currentBlocks.map(block => block.id);
    console.log("[EditorView] Captured authoritative blockOrder from editor:", newBlockOrder);

    // Convert current blocks to comparison format
    const currentBlocksForComparison = currentBlocks.map(block => ({
      id: block.id,
      type: block.type,
      content: block.content
    }));

    // Get the previous blocks for comparison
    const previousBlocks = lastLoadedBlocksRef.current || [];

    // Check if there are actual changes (but allow deletions to proceed)
    const hasContentChanges = !blocksAreEqual(currentBlocksForComparison, previousBlocks);
    const hasBlockCountChange = currentBlocksForComparison.length !== previousBlocks.length;

    if (!hasContentChanges && !hasBlockCountChange) {
      console.log("[EditorView] No actual content changes detected, skipping store update");
      return;
    }

    console.log("[EditorView] Content changes detected, updating store with centralized action");

    // Handle block deletions - find blocks that were in previous but not in current
    const currentBlockIds = new Set(currentBlocks.map(block => block.id));
    const previousBlockIds = new Set(previousBlocks.map(block => block.id));

    // Delete blocks that are no longer present
    previousBlockIds.forEach(blockId => {
      if (!currentBlockIds.has(blockId)) {
        console.log("[EditorView] Deleting removed block:", blockId);
        deleteBlock(blockId);
        // Also queue deletion for auto-save
        blockAutoSaveManager.queueBlockSave(blockId, null, 'delete');
      }
    });

    // Use centralized action to update both blocks and blockOrder atomically
    const store = useDocumentStore.getState();
    store.updateActiveDocumentState(activeFileId, currentBlocks, newBlockOrder);

    // Queue blocks for auto-save
    currentBlocks.forEach((block: any) => {
      if (block && block.id) {
        // Ensure the block has the correct documentId
        const blockWithDocId = {
          ...block,
          documentId: activeFileId
        };

        // Queue the block for auto-save with proper document context
        blockAutoSaveManager.queueBlockSave(block.id, blockWithDocId);
      }
    });

    // Update our reference to the current blocks
    lastLoadedBlocksRef.current = currentBlocksForComparison;
  }, [editor, activeFileId, deleteBlock, blocksAreEqual]);

  // Save file handler using the block-based auto-save system
  const handleSave = useCallback(async () => {
    if (!activeFileId || !editor) {
      console.log('[EditorView] Save skipped: no active file or editor');
      return;
    }

    console.log('[EditorView] Starting manual save for document:', activeFileId);

    try {
      // Set programmatic update flag to prevent onChange during save
      isUpdatingProgrammatically.current = true;

      // Get current editor blocks
      const currentBlocks = editor.document;
      console.log('[EditorView] Current editor blocks:', currentBlocks.length);

      // Set the current document in the auto-save manager
      blockAutoSaveManager.setCurrentDocument(activeFileId);

      // Queue each block for immediate saving (without updating store - blocks should already be there)
      currentBlocks.forEach((block: any, index: number) => {
        if (block && block.id) {
          console.log(`[EditorView] Queuing block ${index + 1}/${currentBlocks.length} for save:`, block.id);

          // Ensure block has documentId and queue for saving
          const blockWithDocId = {
            ...block,
            documentId: activeFileId
          };

          // Queue the block for saving (don't update store to avoid triggering loops)
          blockAutoSaveManager.queueBlockSave(block.id, blockWithDocId);
        }
      });

      console.log('[EditorView] All blocks queued, forcing save...');

      // Force immediate save of all queued operations
      await blockAutoSaveManager.forceSave();

      console.log('[EditorView] Manual save completed successfully');
      showNotification('Document saved successfully!', 'success');
    } catch (error) {
      console.error('[EditorView] Error during manual save:', error);
      showNotification('Failed to save document. Please try again.', 'error');
    } finally {
      // Reset programmatic update flag
      setTimeout(() => {
        isUpdatingProgrammatically.current = false;
      }, 100);
    }
  }, [activeFileId, editor, showNotification]);

  /**
   * Enhanced copy handler following 2025 React best practices
   * Prioritizes browser visual selection over BlockNote block selection
   * Only processes copy events when the editor is focused
   */
  const handleCopy = useCallback(async (e: ClipboardEvent) => {
    console.log('[EditorView] Copy event triggered!');

    // Early validation
    if (!editor || !activeFile) {
      console.log('[EditorView] No editor or active file available');
      return; // Don't prevent default, let other copy operations proceed
    }

    // Check if the copy event originated from within our editor container
    const editorContainer = editorContainerRef.current;
    if (!editorContainer) {
      console.log('[EditorView] No editor container available');
      return; // Don't prevent default, let other copy operations proceed
    }

    // Check if the target element is within our editor container
    const target = e.target as Element;
    if (!target || !editorContainer.contains(target)) {
      console.log('[EditorView] 🚫 COPY EVENT IGNORED (External Source):');
      console.log('  📍 Event target:', target?.tagName || 'unknown');
      console.log('  📚 Dictionary preserved:', Object.keys(hyperlinkDictionaryRef.current).length > 0 ? 'Yes' : 'Empty');
      console.log('  ➡️ Letting external copy proceed normally');
      return; // Don't prevent default, let other copy operations proceed
    }

    console.log('[EditorView] ✅ COPY EVENT FROM OUR EDITOR - PROCESSING:');
    console.log('  📍 Event target:', target?.tagName || 'unknown');
    console.log('  📚 Will process hyperlinks and update dictionary');
    e.preventDefault();

    try {
      // Analyze current selection state
      const selectionState = getSelectionState();
      console.log('[EditorView] Selection analysis:', {
        hasVisualSelection: selectionState.hasActiveVisualSelection,
        textLength: selectionState.rawSelectedText.length
      });

      let content: CopyContent;

      if (selectionState.hasActiveVisualSelection && selectionState.browserSelection) {
        // Path A: Visual text selection (prioritized)
        console.log('[EditorView] Processing visual selection');

        // For visual selections, check if it's within a single block and use BlockNote's HTML generation
        const currentBlock = editor.getTextCursorPosition().block;
        const bnSelection = editor.getSelection();

        // Check if we're dealing with a single block selection (full or partial)
        const isSingleBlockContext = !bnSelection?.blocks.length || bnSelection.blocks.length <= 1;

        if (isSingleBlockContext) {
          console.log('[EditorView] Single block context detected - using BlockNote HTML generation for formatting preservation');

          try {
            // Use BlockNote's HTML generation for the current block to preserve formatting
            const blockHtml = await editor.blocksToHTMLLossy([currentBlock]);
            console.log('[EditorView] Generated BlockNote HTML for single block:', blockHtml.substring(0, 200));

            // For partial selections, we'll use the BlockNote HTML but keep the selected text
            // This ensures formatting is preserved even for partial selections
            content = {
              plainText: selectionState.rawSelectedText,
              html: blockHtml
            };

            console.log('[EditorView] Using BlockNote HTML for single block/partial selection');
          } catch (error) {
            console.error('[EditorView] Error generating BlockNote HTML, falling back to DOM cloning:', error);
            // Fallback to original method
            content = {
              plainText: selectionState.rawSelectedText,
              html: generateHtmlFromSelection(selectionState.browserSelection, selectionState.rawSelectedText)
            };
          }
        } else {
          console.log('[EditorView] Multi-block visual selection - using DOM cloning method');
          content = {
            plainText: selectionState.rawSelectedText,
            html: generateHtmlFromSelection(selectionState.browserSelection, selectionState.rawSelectedText)
          };
        }
      } else {
        // Path B: Block-based selection (fallback)
        console.log('[EditorView] Processing block-based selection');

        const bnSelection = editor.getSelection();
        let blocksToProcess: Block[] = [];

        if (bnSelection?.blocks.length > 0) {
          blocksToProcess = bnSelection.blocks;
        } else if (editor.document.length > 0) {
          blocksToProcess = [editor.getTextCursorPosition().block];
        }

        if (blocksToProcess.length === 0) {
          showNotification(COPY_CONSTANTS.ERROR_MESSAGES.NO_CONTENT, 'info');
          return;
        }

        content = await generateContentFromBlocks(editor, blocksToProcess);
      }

      // Apply system prompt prepending to both plain text and HTML
      const isPrependEnabled = activeFile?.documentSettings?.isSystemPromptPrepended ?? false;
      const systemPrompt = getCurrentSystemPrompt();

      console.log('[EditorView] 📝 PREPEND STATUS:', {
        enabled: isPrependEnabled,
        hasPrompt: !!systemPrompt.trim(),
        promptPreview: systemPrompt.substring(0, 50) + (systemPrompt.length > 50 ? '...' : '')
      });

      content.plainText = applySystemPromptPrepend(
        content.plainText,
        isPrependEnabled,
        systemPrompt
      );

      content.html = applySystemPromptPrependToHtml(
        content.html,
        isPrependEnabled,
        systemPrompt
      );

      // Save hyperlinks if the toggle is enabled
      const isHyperlinkSavingEnabled = activeFile?.documentSettings?.hyperlinkSavingEnabled ?? false;
      console.log('[EditorView] 🔗 HYPERLINK SAVING STATUS:', isHyperlinkSavingEnabled ? 'ENABLED' : 'DISABLED');

      if (isHyperlinkSavingEnabled) {
        console.log('[EditorView] 🔍 EXTRACTING HYPERLINKS FROM COPIED CONTENT:');
        console.log('  📝 Plain text content:', JSON.stringify(content.plainText));
        console.log('  🌐 HTML content:', content.html);

        // Extract hyperlinks and store in simplified dictionary
        let extractedHyperlinks: SimplifiedHyperlinkDictionary = {};

        if (selectionState.hasActiveVisualSelection && selectionState.browserSelection) {
          console.log('[EditorView] 📄 Using visual selection method (HTML parsing)');
          extractedHyperlinks = extractHyperlinksFromHtml(content.html);
          console.log('[EditorView] 🔗 Extracted hyperlinks from HTML:', JSON.stringify(extractedHyperlinks, null, 2));
        } else {
          console.log('[EditorView] 📦 Using BlockNote blocks method');
          const bnSelection = editor.getSelection();
          let blocksToProcess: Block[] = [];

          if (bnSelection?.blocks.length > 0) {
            blocksToProcess = bnSelection.blocks;
            console.log('[EditorView] 📦 Processing selected blocks:', blocksToProcess.length);
          } else if (editor.document.length > 0) {
            blocksToProcess = [editor.getTextCursorPosition().block];
            console.log('[EditorView] 📦 Processing current cursor block');
          }

          console.log('[EditorView] 📦 Blocks to process:', JSON.stringify(blocksToProcess, null, 2));

          if (blocksToProcess.length > 0) {
            extractedHyperlinks = extractHyperlinksFromBlocks(blocksToProcess);
            console.log('[EditorView] 🔗 Extracted hyperlinks from blocks:', JSON.stringify(extractedHyperlinks, null, 2));
          }
        }

        // Overwrite hyperlink dictionary (don't clear, just replace)
        console.log('[EditorView] Previous hyperlink dictionary:', JSON.stringify(hyperlinkDictionaryRef.current, null, 2));
        hyperlinkDictionaryRef.current = extractedHyperlinks;
        console.log('[EditorView] ✅ UPDATED HYPERLINK DICTIONARY:');
        console.log('  📊 New dictionary state:', JSON.stringify(hyperlinkDictionaryRef.current, null, 2));
        console.log('  🔗 Total hyperlinks saved:', Object.keys(extractedHyperlinks).length);
      }

      // Create final HTML document
      const finalHtmlDocument = COPY_CONSTANTS.HTML_TEMPLATE(content.html);

      // Debug logging for clipboard content
      console.log('[EditorView] 📋 CLIPBOARD CONTENT ANALYSIS:');
      console.log('  📝 Plain text length:', content.plainText.length);
      console.log('  📝 Plain text preview:', JSON.stringify(content.plainText.substring(0, 100)));
      console.log('  🌐 HTML content length:', content.html.length);
      console.log('  🌐 HTML content preview:', content.html.substring(0, 200));
      console.log('  📄 Final HTML document length:', finalHtmlDocument.length);
      console.log('  🔖 Prepend applied:', isPrependEnabled && !!systemPrompt.trim());

      // Write to clipboard with dual format
      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([finalHtmlDocument], { type: 'text/html' }),
        'text/plain': new Blob([content.plainText], { type: 'text/plain' })
      });

      await navigator.clipboard.write([clipboardItem]);
      showNotification('Content copied!', 'success');

      console.log('[EditorView] ✅ Copy operation completed successfully with rich formatting');

    } catch (error) {
      console.error('[EditorView] Error during copy operation:', error);

      // Structured error handling with fallback
      try {
        console.log('[EditorView] Attempting fallback to plain text copy');

        // Get fallback content
        const fallbackSelection = getSelectionState();
        let fallbackText: string;

        if (fallbackSelection.hasActiveVisualSelection) {
          fallbackText = fallbackSelection.rawSelectedText;
        } else if (editor.document.length > 0) {
          const currentBlock = editor.getTextCursorPosition().block;
          const markdownContent = await editor.blocksToMarkdownLossy([currentBlock]);
          fallbackText = removeMarkdown(markdownContent);
        } else {
          throw new Error('No content available for fallback copy');
        }

        // Apply prepend to fallback
        const isPrependEnabled = activeFile?.documentSettings?.isSystemPromptPrepended ?? false;
        const systemPrompt = getCurrentSystemPrompt();
        fallbackText = applySystemPromptPrepend(fallbackText, isPrependEnabled, systemPrompt);

        await navigator.clipboard.writeText(fallbackText);
        showNotification(COPY_CONSTANTS.ERROR_MESSAGES.FALLBACK_SUCCESS, 'success');

        console.log('[EditorView] Fallback copy completed successfully');
      } catch (fallbackError) {
        console.error('[EditorView] Fallback copy failed:', fallbackError);
        showNotification(COPY_CONSTANTS.ERROR_MESSAGES.CLIPBOARD_FAILED, 'error');
      }
    }
  }, [editor, activeFile, getCurrentSystemPrompt, showNotification]);

  /**
   * Helper function to get selected blocks for copy operations
   */
  const getSelectedBlocks = useCallback((): Block[] => {
    if (!editor) return [];

    const selectionState = getSelectionState();

    if (selectionState.hasActiveVisualSelection) {
      // For visual selections, get blocks that contain the selection
      const bnSelection = editor.getSelection();
      if (bnSelection?.blocks.length > 0) {
        return bnSelection.blocks;
      } else {
        // Fallback to current block if no block selection
        return [editor.getTextCursorPosition().block];
      }
    } else {
      // Block-based selection
      const bnSelection = editor.getSelection();
      if (bnSelection?.blocks.length > 0) {
        return bnSelection.blocks;
      } else if (editor.document.length > 0) {
        return [editor.getTextCursorPosition().block];
      }
    }

    return [];
  }, [editor]);

  /**
   * Handler for creating a new saved paragraph from selected text
   */
  const handleCreateNewSavedParagraph = useCallback(() => {
    if (!editor) {
      console.log('[EditorView] No editor available for creating saved paragraph');
      return;
    }

    try {
      const selectedBlocks = getSelectedBlocks();

      if (selectedBlocks.length === 0) {
        showNotification('No content selected to save as paragraph', 'warning');
        return;
      }

      console.log('[EditorView] Creating new saved paragraph with blocks:', selectedBlocks);

      // Set the initial content for the create form FIRST
      setInitialCreateContent(selectedBlocks);
      console.log('[EditorView] ✅ Set initial content');

      // Navigate to create mode SECOND
      setCurrentView('create');
      console.log('[EditorView] ✅ Set current view to create');

      // Open the modal LAST
      openModal();
      console.log('[EditorView] ✅ Opened modal');

      console.log('[EditorView] ✅ Successfully opened saved paragraph creation modal with selected content');
    } catch (error) {
      console.error('[EditorView] Error creating new saved paragraph:', error);
      showNotification('Failed to create saved paragraph', 'error');
    }
  }, [editor, getSelectedBlocks, setInitialCreateContent, setCurrentView, openModal, showNotification]);

  /**
   * Consolidated markdown copy function
   */
  const copyAsMarkdown = useCallback(async (includeSystemPrompt: boolean): Promise<void> => {
    if (!editor || !activeFile) {
      console.log('[EditorView] No editor or active file available');
      showNotification('Copy failed: No document available', 'error');
      return;
    }

    try {
      // Get selected blocks using consolidated logic
      const blocksToProcess = getSelectedBlocks();

      if (blocksToProcess.length === 0) {
        showNotification('Nothing to copy', 'info');
        return;
      }

      // Convert blocks to markdown
      let markdownContent = await editor.blocksToMarkdownLossy(blocksToProcess);

      console.log('[EditorView] 🔍 MARKDOWN COPY DEBUG:');
      console.log('  📦 Blocks to process:', blocksToProcess.length);
      console.log('  📝 Raw markdown from BlockNote:', JSON.stringify(markdownContent));
      console.log('  📏 Raw markdown length:', markdownContent.length);
      console.log('  🔢 Newline count in raw markdown:', (markdownContent.match(/\n/g) || []).length);

      // Clean up extra newlines - convert double newlines to single newlines
      // This removes the extra spacing between blocks while preserving formatting
      const cleanedMarkdown = markdownContent
        .replace(/\n\n+/g, '\n') // Replace multiple newlines with single newlines
        .replace(/\n$/, ''); // Remove trailing newline

      console.log('[EditorView] 🧹 CLEANED MARKDOWN:');
      console.log('  📝 Cleaned markdown:', JSON.stringify(cleanedMarkdown));
      console.log('  🔢 Newlines after cleaning:', (cleanedMarkdown.match(/\n/g) || []).length);

      markdownContent = cleanedMarkdown;

      // Apply system prompt if requested
      if (includeSystemPrompt) {
        const systemPrompt = getCurrentSystemPrompt();
        const beforePrepend = markdownContent;
        markdownContent = applySystemPromptPrepend(markdownContent, true, systemPrompt);

        console.log('[EditorView] 📝 MARKDOWN WITH PROMPT DEBUG:');
        console.log('  🎯 System prompt:', JSON.stringify(systemPrompt));
        console.log('  📝 Before prepend:', JSON.stringify(beforePrepend));
        console.log('  📝 After prepend:', JSON.stringify(markdownContent));
        console.log('  🔢 Newlines before prepend:', (beforePrepend.match(/\n/g) || []).length);
        console.log('  🔢 Newlines after prepend:', (markdownContent.match(/\n/g) || []).length);
      } else {
        console.log('[EditorView] 📝 FINAL MARKDOWN (no prompt):', JSON.stringify(markdownContent));
      }

      // Copy to clipboard
      await navigator.clipboard.writeText(markdownContent);

      const message = includeSystemPrompt
        ? 'Markdown with system prompt copied!'
        : 'Markdown copied!';
      showNotification(message, 'success');

      console.log(`[EditorView] ✅ ${message}`);

    } catch (error) {
      console.error('[EditorView] Error during markdown copy:', error);
      const errorMessage = includeSystemPrompt
        ? 'Failed to copy markdown with system prompt'
        : 'Failed to copy markdown';
      showNotification(errorMessage, 'error');
    }
  }, [editor, activeFile, getSelectedBlocks, getCurrentSystemPrompt, showNotification]);

  /**
   * Copy markdown handler - copies content as markdown without system prompt
   */
  const handleCopyMarkdown = useCallback(async () => {
    console.log('[EditorView] Copy markdown triggered!');
    await copyAsMarkdown(false);
  }, [copyAsMarkdown]);

  /**
   * Copy markdown with system prompt handler - always includes system prompt regardless of toggle
   */
  const handleCopyMarkdownWithSystemPrompt = useCallback(async () => {
    console.log('[EditorView] Copy markdown with system prompt triggered!');
    await copyAsMarkdown(true);
  }, [copyAsMarkdown]);

  // Set up keyboard shortcuts for editor commands
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Save: Ctrl+S or Cmd+S
      if ((e.ctrlKey || e.metaKey) && !e.shiftKey && !e.altKey && e.key === 's') {
        e.preventDefault();
        handleSave();
        return;
      }

      // Copy markdown: Shift+C
      if (!e.ctrlKey && !e.metaKey && e.shiftKey && !e.altKey && e.key === 'C') {
        e.preventDefault();
        handleCopyMarkdown();
        return;
      }

      // Copy markdown with system prompt: Ctrl/Cmd+Shift+C
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && !e.altKey && e.key === 'C') {
        e.preventDefault();
        handleCopyMarkdownWithSystemPrompt();
        return;
      }

      // Only process formatting shortcuts when editor is available and in edit/split mode
      if (!editor || mode === 'preview') return;

      const currentBlock = editor.getTextCursorPosition().block;

      // Heading 1: Ctrl/Cmd+Alt+1
      if ((e.ctrlKey || e.metaKey) && e.altKey && e.key === '1') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "heading", props: { level: 1 } });
      }

      // Heading 2: Ctrl/Cmd+Alt+2
      else if ((e.ctrlKey || e.metaKey) && e.altKey && e.key === '2') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "heading", props: { level: 2 } });
      }

      // Heading 3: Ctrl/Cmd+Alt+3
      else if ((e.ctrlKey || e.metaKey) && e.altKey && e.key === '3') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "heading", props: { level: 3 } });
      }

      // Bullet list: Ctrl/Cmd+Shift+8
      else if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === '*') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "bulletListItem" });
      }

      // Numbered list: Ctrl/Cmd+Shift+7
      else if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === '&') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "numberedListItem" });
      }

      // Code block: Ctrl/Cmd+Shift+K (changed from C to avoid conflict)
      else if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'K') {
        e.preventDefault();
        editor.updateBlock(currentBlock, { type: "codeBlock" });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, handleCopyMarkdown, handleCopyMarkdownWithSystemPrompt, editor, mode]);

  // Set up copy event listener (paste is handled by BlockNote's pasteHandler)
  useEffect(() => {
    console.log('[EditorView] Setting up copy event listener');

    // Add copy event listener to the document to catch keyboard shortcuts
    document.addEventListener('copy', handleCopy);

    return () => {
      console.log('[EditorView] Cleaning up copy event listener');
      document.removeEventListener('copy', handleCopy);
    };
  }, [handleCopy]);

  // Handle beforeunload event to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Handle editor mode changes
  const handleModeChange = useCallback((newMode: EditorMode) => {
    setEditorMode(newMode);
  }, [setEditorMode]);

  // If no file is active, show empty state
  if (!activeFile || !activeFileId) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 bg-background text-foreground">
        <FileWarning className="h-16 w-16 text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold mb-2">No document open</h2>
        <p className="text-muted-foreground text-center max-w-md">
          Select a document from the sidebar or create a new one to start editing.
        </p>
      </div>
    );
  }

  // Render editor once it's ready
  if (!editor) {
    return (
      <div className="p-6">
        <Skeleton className="h-8 w-full mb-4" />
        <Skeleton className="h-4 w-3/4 mb-3" />
        <Skeleton className="h-4 w-5/6 mb-3" />
        <Skeleton className="h-4 w-2/3 mb-3" />
      </div>
    );
  }

  return (
    <>
      {/* Enhanced Editor Toolbar - Fixed at top */}
      <EditorToolbar
        editor={editor}
        mode={mode}
        hasUnsavedChanges={hasUnsavedChanges}
        saveStatus={saveStatus}
        onModeChange={handleModeChange}
        onSave={handleSave}
      />

      {/* Editor Content with Split View Support */}
      <div
        className="flex flex-col w-full h-full bg-background relative"
        ref={editorContainerRef}
        style={editorContainerStyles}
      >
        <div className="flex-1 overflow-hidden block">
          <BlockNoteView
            editor={editor}
            onChange={handleEditorChange}
            editable={mode !== 'preview'}
            className="h-full max-w-4xl mx-auto py-4"
            theme={getBlockNoteTheme()}
            formattingToolbar={false}
            slashMenu={false} // Disable default slash menu
          >
            <SuggestionMenuController
              triggerCharacter={"/"}
              getItems={async (query) => {
                const items = getCustomSlashMenuItems(editor);
                if (!query) {
                  return items; // Return all items if query is empty
                }
                const lowerCaseQuery = query.toLowerCase();
                return items.filter(item =>
                  item.title.toLowerCase().includes(lowerCaseQuery) ||
                  (item.aliases || []).some(alias => alias.toLowerCase().includes(lowerCaseQuery)) ||
                  (item.subtext || "").toLowerCase().includes(lowerCaseQuery) // Also search subtext
                );
              }}
            />
            {mode !== 'preview' && (
              <FormattingToolbarController
                formattingToolbar={() => (
                  <FormattingToolbar>
                    <BlockTypeSelect key={"blockTypeSelect"} />

                    <RestyleButton key={"restyleButton"} />

                    <BasicTextStyleButton
                      basicTextStyle={"bold"}
                      key={"boldStyleButton"}
                    />
                    <BasicTextStyleButton
                      basicTextStyle={"italic"}
                      key={"italicStyleButton"}
                    />
                    <BasicTextStyleButton
                      basicTextStyle={"underline"}
                      key={"underlineStyleButton"}
                    />
                    <BasicTextStyleButton
                      basicTextStyle={"strike"}
                      key={"strikeStyleButton"}
                    />

                    <ColorStyleButton key={"colorStyleButton"} />

                    <TextAlignButton
                      textAlignment={"left"}
                      key={"textAlignLeftButton"}
                    />
                    <TextAlignButton
                      textAlignment={"center"}
                      key={"textAlignCenterButton"}
                    />
                    <TextAlignButton
                      textAlignment={"right"}
                      key={"textAlignRightButton"}
                    />

                    <NestBlockButton key={"nestBlockButton"} />
                    <UnnestBlockButton key={"unnestBlockButton"} />

                    <CreateLinkButton key={"createLinkButton"} />

                    {/* TODO: Implement functionality for these buttons */}
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button key={"createNewSavedParagraphButton"} onClick={handleCreateNewSavedParagraph} className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                            <PlusSquare size={18} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                          <p>Create New Saved Paragraph</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button key={"updateExistingSavedParagraphButton"} onClick={() => console.log('Update existing saved paragraph clicked (not implemented)')} className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                            <Save size={18} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                          <p>Update Existing Saved Paragraph</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormattingToolbar>
                )}
              />
            )}
          </BlockNoteView>
        </div>
      </div>
    </>
  );
};

export default React.memo(EditorView);
