import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { CheckCircle, AlertCircle, Info, X } from 'lucide-react';
import { cn } from '../lib/utils';

// Import the Shadcn toast components from the correct local paths
import { useToast } from "../hooks/use-toast";
import { Toaster } from "../components/ui/toaster";
import type { Toast } from "../components/ui/toast";

export type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface NotificationContextType {
  showNotification: (message: string, type?: NotificationType, duration?: number) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { toast } = useToast();

  // Show a notification using Shadcn's toast system
  const showNotification = useCallback((message: string, type: NotificationType = 'info', duration: number = 2000) => {
    // Map our notification types to Shadcn variants - now we support warning too
    const variant = type === 'error' ? 'destructive' :
                   type === 'warning' ? 'warning' : 'default';

    // Get the appropriate icon based on notification type
    const Icon = {
      success: CheckCircle,
      error: AlertCircle,
      warning: AlertCircle,
      info: Info
    }[type];

    // Create title string for better visual hierarchy
    const titleText = type.charAt(0).toUpperCase() + type.slice(1);

    // Use the Shadcn toast system
    toast({
      variant: variant,
      title: titleText,
      description: message,
      duration: duration,
      // Add custom styling to make it match our design based on notification type
      className: cn(
        "border",
        type === 'success' && "bg-green-50 dark:bg-green-900/30 border-green-300 dark:border-green-700 text-green-700 dark:text-green-300",
        type === 'warning' && "bg-amber-50 dark:bg-amber-900/30 border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300",
        type === 'info' && "bg-blue-50 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300"
      )
    });
  }, [toast]);

  return (
    <NotificationContext.Provider value={{ showNotification }}>
      {children}

      {/* Use the Shadcn UI Toaster component for the notification system */}
      <Toaster />
    </NotificationContext.Provider>
  );
};

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};


