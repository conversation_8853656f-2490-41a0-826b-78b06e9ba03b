import React, { useCallback, useEffect } from 'react';
import { ChevronRight, ChevronLeft } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { FrontendStoredFile } from './../../../../types/global';
import { useNotification } from '../../../contexts/NotificationContext';
import { useSidebarContext } from '../../../contexts/SidebarContext';

// Import selectors instead of using the store directly
import {
  useFiles,
  useCurrentStoragePath,
  useActiveFileId,
  useDocumentStore
} from '../../../stores/blockDocumentStore';

// Import types and components
import { SidebarProps, FileNode } from './types';
import { useSidebarState } from './hooks/useSidebarState';
import { Modal } from './components/Modal';
import { SidebarHeader } from './components/SidebarHeader';
import { SearchInput } from './components/SearchInput';
import { TabButtons } from './components/TabButtons';
import { SidebarContent } from './components/SidebarContent';

// Main HoverSidebar component
const HoverSidebarLeft = ({ onSelectStorageFolder: propOnSelectStorageFolder }: SidebarProps) => {
  // Use selectors from the Zustand store for optimized renders
  const files = useFiles();
  const currentStoragePath = useCurrentStoragePath();
  const currentDocId = useActiveFileId();
  const { showNotification } = useNotification();
  const { setLeftSidebarExpanded } = useSidebarContext();

  // Get methods from the store
  const {
    loadFile,
    deleteFile,
    renameFile,
    selectStorageDirectory,
    refreshFiles,
    createDocument,
    createFolder,
    moveItem,
    openTab
  } = useDocumentStore();

  // Use custom hook for state management
  const {
    expanded,
    isPinned,
    fileTree,
    setFileTree,
    selectedTab,
    searchTerm,
    expandAll,
    isUiExtensionOpen,
    setIsUiExtensionOpen,
    sortOrder,
    bookmarkedFiles,
    setBookmarkedFiles,
    recentFiles,
    setRecentFiles,
    loading,
    setLoading,
    filteredFileTree,
    deleteDialogOpen,
    setDeleteDialogOpen,
    deleteTarget,
    setDeleteTarget,
    renameDialogOpen,
    setRenameDialogOpen,
    renameTarget,
    setRenameTarget,
    newNameInput,
    setNewNameInput,
    newFileDialogOpen,
    setNewFileDialogOpen,
    newFolderDialogOpen,
    setNewFolderDialogOpen,
    newItemParentPath,
    setNewItemParentPath,
    newItemName,
    setNewItemName,
    setSelectedTab,
    setSearchTerm,
    setSortOrder,
    toggleExpanded,
    togglePin,
    handleMouseEnterSidebar,
    handleHoverStripEnter,
    handleSidebarLeave,
    toggleExpandCollapseAll,
    handleToggleNodeExpansion,
  } = useSidebarState(files);

  // Sync sidebar expanded state with context
  useEffect(() => {
    setLeftSidebarExpanded(expanded);
  }, [expanded, setLeftSidebarExpanded]);

  // Define handlers with useCallback for memoization
  const onFileOpen = useCallback((fileId: string) => {
    loadFile(fileId).then(file => {
      if (file) openTab(file);
    }).catch(error => {
      console.error(`Error opening file ${fileId}:`, error);
      showNotification('Failed to open file', 'error');
    });
  }, [loadFile, openTab, showNotification]);

  const onFileDelete = useCallback(async (fileId: string) => {
    try {
      await deleteFile(fileId);
      return true;
    } catch (error) {
      console.error(`Error deleting file ${fileId}:`, error);
      showNotification('Failed to delete file', 'error');
      return false;
    }
  }, [deleteFile, showNotification]);

  const onFileRename = useCallback(async (fileId: string, newName: string) => {
    try {
      await renameFile(fileId, newName);
      return true;
    } catch (error) {
      console.error(`Error renaming file ${fileId}:`, error);
      showNotification('Failed to rename file', 'error');
      throw error;
    }
  }, [renameFile, showNotification]);

  const onSelectStorageFolder = useCallback(() => {
    if (propOnSelectStorageFolder) {
      propOnSelectStorageFolder();
    } else {
      selectStorageDirectory().catch(error => {
        console.error('Error selecting storage directory:', error);
        showNotification('Failed to select storage directory', 'error');
      });
    }
  }, [propOnSelectStorageFolder, selectStorageDirectory, showNotification]);

  const onRefresh = useCallback(async () => {
    try {
      setLoading(true);
      await refreshFiles();
      showNotification('Files refreshed successfully', 'success');
      return true;
    } catch (error) {
      console.error('Error refreshing files:', error);
      showNotification('Failed to refresh files', 'error');
      return false;
    } finally {
      setLoading(false);
    }
  }, [refreshFiles, showNotification, setLoading]);

  const onCreateDocument = useCallback(async (name: string, parentId: string | null = null) => {
    try {
      return await createDocument(name, parentId);
    } catch (error) {
      console.error('Error creating document:', error);
      showNotification('Failed to create document', 'error');
      throw error;
    }
  }, [createDocument, showNotification]);

  const onCreateFolder = useCallback(async (name: string, parentId: string | null = null) => {
    try {
      return await createFolder(name, parentId);
    } catch (error) {
      console.error('Error creating folder:', error);
      showNotification('Failed to create folder', 'error');
      throw error;
    }
  }, [createFolder, showNotification]);

  const onItemMove = useCallback(async (itemId: string, newParentId: string | null): Promise<void> => {
    try {
      await moveItem(itemId, newParentId);
      // Function returns void as expected by the interface
    } catch (error) {
      console.error('Error moving item:', error);
      showNotification('Failed to move item', 'error');
      throw error;
    }
  }, [moveItem, showNotification]);

  // Handle opening a file
  const handleOpenFile = useCallback((node: FileNode) => {
    if (node.type !== 'file') return;

    // Optimistic update: Update UI first
    const updatedRecentFiles = [
      node,
      ...recentFiles.filter(f => f.id !== node.id)
    ].slice(0, 10);

    setRecentFiles(updatedRecentFiles);

    // Then perform the actual operation
    onFileOpen(node.id);
    showNotification(`Opening ${node.name}...`, 'info');
  }, [onFileOpen, recentFiles, setRecentFiles, showNotification]);

  // Handle deleting a node
  const handleDeleteNode = useCallback(async () => {
    if (!deleteTarget) return;

    // Optimistic update: Update UI first
    setFileTree(prevTree => {
      const removeNode = (nodes: FileNode[]): FileNode[] => {
        return nodes.filter(n => {
          if (n.id === deleteTarget.id) return false;
          if (n.children && n.children.length > 0) {
            n.children = removeNode(n.children);
          }
          return true;
        });
      };
      return removeNode(prevTree);
    });

    // Also remove from bookmarks and recent files if present
    setBookmarkedFiles(prev => prev.filter(f => f.id !== deleteTarget.id));
    setRecentFiles(prev => prev.filter(f => f.id !== deleteTarget.id));

    showNotification(`Deleting ${deleteTarget.name}...`, 'info');

    // Perform the actual delete operation
    try {
      const success = await onFileDelete(deleteTarget.id);
      if (success) {
        showNotification(`Deleted ${deleteTarget.name}`, 'success');
      }
    } catch (error) {
      console.error(`Error deleting ${deleteTarget.name}:`, error);
      showNotification(`Failed to delete ${deleteTarget.name}`, 'error');

      // Since we did an optimistic update, we need to refresh to restore the correct state
      onRefresh();
    }

    setDeleteDialogOpen(false);
    setDeleteTarget(null);
  }, [deleteTarget, onFileDelete, onRefresh, showNotification, setFileTree, setBookmarkedFiles, setRecentFiles, setDeleteDialogOpen, setDeleteTarget]);

  // Handle creating a new file with optimistic updates
  const handleCreateNewFile = useCallback(async () => {
    if (!newItemName.trim()) return;

    const name = newItemName.trim();
    const parentId = newItemParentPath || null;

    // Close dialogs first for better UX
    setNewFileDialogOpen(false);
    setNewItemName('');
    setNewItemParentPath('');
    setIsUiExtensionOpen(false);

    showNotification(`Creating new document: ${name}...`, 'info');

    // Create a temporary ID for optimistic update
    const tempId = `temp-${Date.now()}`;
    const now = new Date().toISOString();

    // Create a temporary node for optimistic update
    const tempNode: FileNode = {
      id: tempId,
      name,
      type: 'file',
      children: [],
      parentId,
      updatedAt: now,
      isExpanded: false
    };

    // Optimistically add the new file to the tree
    setFileTree(prevTree => {
      const addNode = (nodes: FileNode[]): FileNode[] => {
        // If adding to root
        if (!parentId) {
          return [...nodes, tempNode];
        }

        // If adding to a parent folder
        return nodes.map(n => {
          if (n.id === parentId) {
            return {
              ...n,
              isExpanded: true, // Expand the parent folder
              children: [...n.children, tempNode]
            };
          }
          if (n.children && n.children.length > 0) {
            return { ...n, children: addNode(n.children) };
          }
          return n;
        });
      };

      return addNode(prevTree);
    });

    // Now perform the actual creation
    try {
      if (onCreateDocument) {
        const newDoc = await onCreateDocument(name, parentId);
        showNotification(`Created new document: ${name}`, 'success');

        // Replace the temporary node with the real one (through a refresh)
        // A more efficient approach could update just this node, but refresh is simpler
        onRefresh();
      } else {
        throw new Error("Document creation functionality not available");
      }
    } catch (error) {
      console.error("Error creating document:", error);
      showNotification('Failed to create document', 'error');

      // Remove the temporary node on failure
      setFileTree(prevTree => {
        const removeTemp = (nodes: FileNode[]): FileNode[] => {
          return nodes.filter(n => {
            if (n.id === tempId) return false;
            if (n.children && n.children.length > 0) {
              n.children = removeTemp(n.children);
            }
            return true;
          });
        };
        return removeTemp(prevTree);
      });
    }
  }, [newItemName, newItemParentPath, onCreateDocument, onRefresh, showNotification, setFileTree, setNewFileDialogOpen, setNewItemName, setNewItemParentPath, setIsUiExtensionOpen]);

  // Handle creating a new folder with optimistic updates
  const handleCreateNewFolder = useCallback(async () => {
    if (!newItemName.trim()) return;

    const name = newItemName.trim();
    const parentId = newItemParentPath || null;

    // Close dialogs first for better UX
    setNewFolderDialogOpen(false);
    setNewItemName('');
    setNewItemParentPath('');
    setIsUiExtensionOpen(false);

    showNotification(`Creating new folder: ${name}...`, 'info');

    // Create a temporary ID for optimistic update
    const tempId = `temp-${Date.now()}`;
    const now = new Date().toISOString();

    // Create a temporary node for optimistic update
    const tempNode: FileNode = {
      id: tempId,
      name,
      type: 'folder',
      children: [],
      parentId,
      updatedAt: now,
      isExpanded: false
    };

    // Optimistically add the new folder to the tree
    setFileTree(prevTree => {
      const addNode = (nodes: FileNode[]): FileNode[] => {
        // If adding to root
        if (!parentId) {
          return [...nodes, tempNode];
        }

        // If adding to a parent folder
        return nodes.map(n => {
          if (n.id === parentId) {
            return {
              ...n,
              isExpanded: true, // Expand the parent folder
              children: [...n.children, tempNode]
            };
          }
          if (n.children && n.children.length > 0) {
            return { ...n, children: addNode(n.children) };
          }
          return n;
        });
      };

      return addNode(prevTree);
    });

    // Now perform the actual creation
    try {
      if (onCreateFolder) {
        const newFolder = await onCreateFolder(name, parentId);
        showNotification(`Created new folder: ${name}`, 'success');

        // Replace the temporary node with the real one (through a refresh)
        onRefresh();
      } else {
        throw new Error("Folder creation functionality not available");
      }
    } catch (error) {
      console.error("Error creating folder:", error);
      showNotification('Failed to create folder', 'error');

      // Remove the temporary node on failure
      setFileTree(prevTree => {
        const removeTemp = (nodes: FileNode[]): FileNode[] => {
          return nodes.filter(n => {
            if (n.id === tempId) return false;
            if (n.children && n.children.length > 0) {
              n.children = removeTemp(n.children);
            }
            return true;
          });
        };
        return removeTemp(prevTree);
      });
    }
  }, [newItemName, newItemParentPath, onCreateFolder, onRefresh, showNotification, setFileTree, setNewFolderDialogOpen, setNewItemName, setNewItemParentPath, setIsUiExtensionOpen]);

  // Toggle bookmark status for a file
  const toggleBookmark = useCallback((node: FileNode) => {
    if (node.type !== 'file') return;

    const isBookmarked = bookmarkedFiles.some(f => f.id === node.id);

    if (isBookmarked) {
      setBookmarkedFiles(prev => prev.filter(f => f.id !== node.id));
      showNotification(`Removed ${node.name} from bookmarks`, 'info');
    } else {
      setBookmarkedFiles(prev => [...prev, node]);
      showNotification(`Added ${node.name} to bookmarks`, 'success');
    }
  }, [bookmarkedFiles, setBookmarkedFiles, showNotification]);

  // Node callback handlers
  const handleNodeFileOpen = useCallback((fileId: string) => {
    const node = fileTree.find(n => n.id === fileId) ||
                 fileTree.flatMap(n => getAllChildren(n)).find(n => n.id === fileId);
    if (node) {
      handleOpenFile(node);
    }
  }, [fileTree, handleOpenFile]);

  const handleNodeCreateFile = useCallback((parentId?: string) => {
    setNewItemParentPath(parentId || '');
    setNewFileDialogOpen(true);
    setIsUiExtensionOpen(true);
  }, [setNewItemParentPath, setNewFileDialogOpen, setIsUiExtensionOpen]);

  const handleNodeCreateFolder = useCallback((parentId?: string) => {
    setNewItemParentPath(parentId || '');
    setNewFolderDialogOpen(true);
    setIsUiExtensionOpen(true);
  }, [setNewItemParentPath, setNewFolderDialogOpen, setIsUiExtensionOpen]);

  const handleNodeRename = useCallback((node: FileNode) => {
    setRenameTarget(node);
    setNewNameInput(node.name);
    setRenameDialogOpen(true);
  }, [setRenameTarget, setNewNameInput, setRenameDialogOpen]);

  const handleNodeDelete = useCallback((node: FileNode) => {
    setDeleteTarget(node);
    setDeleteDialogOpen(true);
  }, [setDeleteTarget, setDeleteDialogOpen]);

  // Helper function to get all children recursively
  const getAllChildren = (node: FileNode): FileNode[] => {
    const children = [...node.children];
    node.children.forEach(child => {
      children.push(...getAllChildren(child));
    });
    return children;
  };

  // If sidebar is collapsed, show only the hover strip (bottom third only)
  if (!expanded) {
    return (
      <div className="absolute left-0 z-30" style={{
        top: 'calc(30px + 2 * (100vh - 30px) / 3)',
        height: 'calc((100vh - 30px) / 3)'
      }}>
        <div
          className="w-8 h-full bg-background border-r-2 border-t-2 border-border flex items-center justify-center cursor-pointer hover:bg-muted transition-colors"
          onMouseEnter={handleHoverStripEnter}
          onClick={toggleExpanded}
          title="Expand Sidebar"
        >
          <ChevronRight className="h-4 w-4 text-primary" />
        </div>
      </div>
    );
  }

  return (
    <>
      <AnimatePresence>
        {expanded ? (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 350, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed left-0 z-40 flex flex-col bg-background border-r border-t border-border shadow-lg"
            style={{
              top: '30px', // Full height when expanded
              height: 'calc(100vh - 30px)', // Full height when expanded
              pointerEvents: 'auto'
            }}
            onMouseLeave={handleSidebarLeave}
            onMouseEnter={handleMouseEnterSidebar}
          >
            {/* Header */}
            <SidebarHeader
              isPinned={isPinned}
              onTogglePin={togglePin}
              currentStoragePath={currentStoragePath}
              onSelectStorageFolder={onSelectStorageFolder}
              onCreateFile={() => handleNodeCreateFile()}
              onCreateFolder={() => handleNodeCreateFolder()}
              onRefresh={onRefresh}
              loading={loading}
              sortOrder={sortOrder}
              setSortOrder={setSortOrder}
              expandAll={expandAll}
              toggleExpandCollapseAll={toggleExpandCollapseAll}
              onToggleExpanded={toggleExpanded}
            />

            {/* Search input */}
            <SearchInput searchTerm={searchTerm} setSearchTerm={setSearchTerm} />

            {/* Tabs */}
            <TabButtons selectedTab={selectedTab} setSelectedTab={setSelectedTab} />

            {/* Content area */}
            <SidebarContent
              selectedTab={selectedTab}
              currentStoragePath={currentStoragePath}
              loading={loading}
              filteredFileTree={filteredFileTree}
              recentFiles={recentFiles}
              bookmarkedFiles={bookmarkedFiles}
              searchTerm={searchTerm}
              currentDocId={currentDocId}
              onFileOpen={handleNodeFileOpen}
              onToggleExpansion={handleToggleNodeExpansion}
              onCreateFile={handleNodeCreateFile}
              onCreateFolder={handleNodeCreateFolder}
              onRename={handleNodeRename}
              onDelete={handleNodeDelete}
              onToggleBookmark={toggleBookmark}
              onItemMove={onItemMove}
            />
          </motion.div>
        ) : null}
      </AnimatePresence>

      {/* Delete confirmation dialog */}
      <Modal
        isOpen={deleteDialogOpen}
        onClose={() => { setDeleteDialogOpen(false); setDeleteTarget(null); }}
        title={`Delete ${deleteTarget?.type === 'folder' ? 'Folder' : 'Document'}`}
        footer={
          <>
            <button
              className="px-4 py-2.5 rounded-lg bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-colors font-medium"
              onClick={() => { setDeleteDialogOpen(false); setDeleteTarget(null); }}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2.5 rounded-lg bg-destructive text-destructive-foreground hover:bg-destructive/90 transition-colors font-medium"
              onClick={handleDeleteNode}
            >
              Delete {deleteTarget?.type === 'folder' ? 'Folder' : 'Document'}
            </button>
          </>
        }
      >
        <div className="space-y-4">
          <p className="text-foreground leading-relaxed">
            Are you sure you want to delete <span className="font-medium">"{deleteTarget?.name}"</span>? This action cannot be undone.
          </p>
          {deleteTarget?.type === 'folder' && (
            <p className="text-sm text-muted-foreground leading-relaxed">
              All contents within this folder will also be deleted.
            </p>
          )}
        </div>
      </Modal>

      {/* Rename dialog */}
      <Modal
        isOpen={renameDialogOpen}
        onClose={() => { setRenameDialogOpen(false); setRenameTarget(null); setNewNameInput(''); }}
        title={`Rename ${renameTarget?.type === 'folder' ? 'Folder' : 'Document'}`}
        footer={
          <>
            <button
              className="px-4 py-2.5 rounded-lg bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-colors font-medium"
              onClick={() => { setRenameDialogOpen(false); setRenameTarget(null); setNewNameInput(''); }}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2.5 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={async () => {
                if (!renameTarget || !newNameInput.trim()) return;
                if (newNameInput.trim() === renameTarget.name) {
                  setRenameDialogOpen(false);
                  setRenameTarget(null);
                  setNewNameInput('');
                  return;
                }

                const oldName = renameTarget.name;
                const newName = newNameInput.trim();

                // Close the dialog first
                setRenameDialogOpen(false);
                setRenameTarget(null);
                setNewNameInput('');

                // Optimistic update
                setFileTree(prevTree => {
                  const updateNode = (nodes: FileNode[]): FileNode[] => {
                    return nodes.map(n => {
                      if (n.id === renameTarget.id) {
                        return { ...n, name: newName };
                      }
                      if (n.children && n.children.length > 0) {
                        return { ...n, children: updateNode(n.children) };
                      }
                      return n;
                    });
                  };
                  return updateNode(prevTree);
                });

                if (onFileRename) {
                  try {
                    await onFileRename(renameTarget.id, newName);
                    showNotification(`Renamed to ${newName}`, 'success');
                  } catch (error: any) {
                    console.error('Error renaming item from modal:', error);
                    showNotification(`Failed to rename: ${error.message}`, 'error');

                    // Revert the optimistic update
                    setFileTree(prevTree => {
                      const revertNode = (nodes: FileNode[]): FileNode[] => {
                        return nodes.map(n => {
                          if (n.id === renameTarget.id) {
                            return { ...n, name: oldName };
                          }
                          if (n.children && n.children.length > 0) {
                            return { ...n, children: revertNode(n.children) };
                          }
                          return n;
                        });
                      };
                      return revertNode(prevTree);
                    });
                  }
                }
              }}
              disabled={!newNameInput.trim() || newNameInput.trim() === renameTarget?.name}
            >
              Rename
            </button>
          </>
        }
      >
        <div className="space-y-6">
          <div className="space-y-3">
            <label htmlFor="newName" className="block text-sm font-medium text-foreground">
              Enter a new name for <span className="font-medium">"{renameTarget?.name}"</span>
            </label>
            <input
              id="newName"
              type="text"
              value={newNameInput}
              onChange={(e) => setNewNameInput(e.target.value)}
              className="w-full px-3 py-2.5 border border-input rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-background text-foreground transition-colors"
              onKeyDown={async (e) => {
                if (e.key === 'Enter') {
                  if (!renameTarget || !newNameInput.trim() || newNameInput.trim() === renameTarget.name) {
                    if (newNameInput.trim() === renameTarget?.name) { // If name is unchanged, just close
                      setRenameDialogOpen(false);
                      setRenameTarget(null);
                      setNewNameInput('');
                    }
                    return; // If empty or invalid, do nothing on Enter
                  }

                  const oldName = renameTarget.name;
                  const newName = newNameInput.trim();

                  // Close the dialog first
                  setRenameDialogOpen(false);
                  setRenameTarget(null);
                  setNewNameInput('');

                  // Optimistic update
                  setFileTree(prevTree => {
                    const updateNode = (nodes: FileNode[]): FileNode[] => {
                      return nodes.map(n => {
                        if (n.id === renameTarget.id) {
                          return { ...n, name: newName };
                        }
                        if (n.children && n.children.length > 0) {
                          return { ...n, children: updateNode(n.children) };
                        }
                        return n;
                      });
                    };
                    return updateNode(prevTree);
                  });

                  if (onFileRename) {
                    try {
                      await onFileRename(renameTarget.id, newName);
                      showNotification(`Renamed to ${newName}`, 'success');
                    } catch (error: any) {
                      console.error('Error renaming item from modal (Enter key):', error);
                      showNotification(`Failed to rename: ${error.message}`, 'error');

                      // Revert the optimistic update
                      setFileTree(prevTree => {
                        const revertNode = (nodes: FileNode[]): FileNode[] => {
                          return nodes.map(n => {
                            if (n.id === renameTarget.id) {
                              return { ...n, name: oldName };
                            }
                            if (n.children && n.children.length > 0) {
                              return { ...n, children: revertNode(n.children) };
                            }
                            return n;
                          });
                        };
                        return revertNode(prevTree);
                      });
                    }
                  }
                }
              }}
              autoFocus
            />
          </div>
        </div>
      </Modal>

      {/* New file dialog */}
      <Modal
        isOpen={newFileDialogOpen}
        onClose={() => {
          setNewFileDialogOpen(false);
          setNewItemName('');
          setNewItemParentPath('');
          setIsUiExtensionOpen(false);
        }}
        title="Create New Document"
        footer={
          <>
            <button
              className="px-4 py-2.5 rounded-lg bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-colors font-medium"
              onClick={() => {
                setNewFileDialogOpen(false);
                setNewItemName('');
                setNewItemParentPath('');
                setIsUiExtensionOpen(false);
              }}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2.5 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleCreateNewFile}
              disabled={!newItemName.trim()}
            >
              Create Document
            </button>
          </>
        }
      >
        <div className="space-y-6">
          <div className="space-y-3">
            <label htmlFor="newFileName" className="block text-sm font-medium text-foreground">
              Document Name
            </label>
            <input
              id="newFileName"
              type="text"
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              placeholder="Enter document name (e.g., My Notes)"
              className="w-full px-3 py-2.5 border border-input rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-background text-foreground placeholder:text-muted-foreground transition-colors"
              onKeyDown={(e) => { if (e.key === 'Enter' && newItemName.trim()) { handleCreateNewFile(); } }}
              autoFocus
            />
            <p className="text-xs text-muted-foreground leading-relaxed">
              {newItemParentPath
                ? `Will be created in: ${files.find(f => f.id === newItemParentPath)?.name || 'Unknown folder'}`
                : 'Will be created in the root folder'
              }
            </p>
          </div>
        </div>
      </Modal>

      {/* New folder dialog */}
      <Modal
        isOpen={newFolderDialogOpen}
        onClose={() => {
          setNewFolderDialogOpen(false);
          setNewItemName('');
          setNewItemParentPath('');
          setIsUiExtensionOpen(false);
        }}
        title="Create New Folder"
        footer={
          <>
            <button
              className="px-4 py-2.5 rounded-lg bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-colors font-medium"
              onClick={() => {
                setNewFolderDialogOpen(false);
                setNewItemName('');
                setNewItemParentPath('');
                setIsUiExtensionOpen(false);
              }}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2.5 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleCreateNewFolder}
              disabled={!newItemName.trim()}
            >
              Create Folder
            </button>
          </>
        }
      >
        <div className="space-y-6">
          <div className="space-y-3">
            <label htmlFor="newFolderName" className="block text-sm font-medium text-foreground">
              Folder Name
            </label>
            <input
              id="newFolderName"
              type="text"
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              placeholder="Enter folder name"
              className="w-full px-3 py-2.5 border border-input rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-background text-foreground placeholder:text-muted-foreground transition-colors"
              onKeyDown={(e) => { if (e.key === 'Enter' && newItemName.trim()) { handleCreateNewFolder(); } }}
              autoFocus
            />
            <p className="text-xs text-muted-foreground leading-relaxed">
              {newItemParentPath
                ? `Will be created inside: ${files.find(f => f.id === newItemParentPath)?.name || 'Unknown folder'}`
                : 'Will be created in the root folder'
              }
            </p>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default HoverSidebarLeft;
