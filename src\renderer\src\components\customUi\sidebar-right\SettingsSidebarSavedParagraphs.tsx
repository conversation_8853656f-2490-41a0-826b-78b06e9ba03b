import React, { useCallback } from 'react';
import { But<PERSON> } from '../../ui/button';
import { Settings, BookOpen } from 'lucide-react';
import { SavedParagraphsSectionProps } from './SettingsSidebarTypes'; // Import the new props type
import { useSavedParagraphsStore } from '../../../stores/savedParagraphsStore';

export function SettingsSidebarSavedParagraphs({ setIsUiExtensionOpen }: SavedParagraphsSectionProps) { // Accept the prop
  const { openModal } = useSavedParagraphsStore();

  const handleOpenModal = useCallback(() => {
    openModal();
    setIsUiExtensionOpen(true); // Inform context that a UI extension is open
  }, [openModal, setIsUiExtensionOpen]);

  return (
    <>
      <div className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-foreground">Saved Paragraphs</h4>
        </div>

        <div className="space-y-2">
          <Button
            variant="outline"
            onClick={handleOpenModal} // Use new handler
            className="w-full max-w-[240px] justify-center border border-foreground/50 hover:border-foreground/30"
          >
            <Settings className="h-4 w-4 mr-2" />
            Manage Paragraphs
          </Button>

          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex items-center gap-2">
              <BookOpen className="h-3 w-3" />
              <span>Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">/</kbd> to insert saved paragraphs</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
