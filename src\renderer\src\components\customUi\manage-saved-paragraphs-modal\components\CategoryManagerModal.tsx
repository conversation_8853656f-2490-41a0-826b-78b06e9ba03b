import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>alogHeader, DialogTitle } from "../../../ui/dialog";
import { Button } from "../../../ui/button";
import { Input } from "../../../ui/input";
import { <PERSON><PERSON><PERSON><PERSON> } from "../../../ui/scroll-area";
import { Badge } from "../../../ui/badge";
import {
  Plus,
  Edit2,
  Trash2,
  Check,
  X,
  Palette,
  AlertCircle
} from 'lucide-react';
import { cn } from '../../../../lib/utils';
import { SavedParagraphCategory } from '../../../../../types/global';
import { useCategoryManagement } from '../hooks/useCategoryManagement';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '../../../ui/tooltip';
import {
  Alert<PERSON><PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../../ui/alert-dialog";

interface CategoryManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: SavedParagraphCategory[];
  onCreateCategory: (name: string, color?: string) => Promise<boolean>;
  onUpdateCategory: (id: string, updates: Partial<SavedParagraphCategory>) => Promise<boolean>;
  onDeleteCategory: (id: string, name: string) => Promise<boolean>;
}

// Predefined category colors - nice pastel colors that work on light and dark themes
const CATEGORY_COLORS = [
  '#6B8DD6', // Soft blue
  '#8FBC8F', // Sage green
  '#DDA0DD', // Plum purple
  '#F0A985', // Peach orange
  '#87CEEB', // Sky blue
  '#98D8C8', // Mint green
  '#F7DC6F', // Soft yellow
  '#D8A7CA', // Lavender pink
  '#A2C4C9', // Dusty teal
  '#C5A3FF', // Light purple
];

interface CategoryItemProps {
  category: SavedParagraphCategory;
  paragraphCount: number;
  isDefault: boolean;
  onEdit: (category: SavedParagraphCategory) => void;
  onDelete: (category: SavedParagraphCategory) => void;
}

const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  paragraphCount,
  isDefault,
  onEdit,
  onDelete
}) => {
  return (
    <div className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        {/* Color indicator */}
        <div
          className="w-4 h-4 rounded-full border border-border flex-shrink-0"
          style={{ backgroundColor: category.color || '#A2C4C9' }}
        />

        {/* Category info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium text-foreground truncate">
              {category.name}
            </span>
            {isDefault && (
              <Badge variant="secondary" className="text-xs">
                Default
              </Badge>
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            {paragraphCount} paragraph{paragraphCount !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex items-center gap-1 flex-shrink-0">
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(category)}
                className="h-8 w-8 bg-transparent hover:bg-transparent"
              >
                <Edit2 className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Edit category</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {!isDefault && (
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onDelete(category)}
                  className="h-8 w-8 bg-transparent hover:bg-transparent"
                >
                  <Trash2 className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Delete category</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
};

interface CategoryFormProps {
  category?: SavedParagraphCategory;
  onSave: (name: string, color: string) => Promise<void>;
  onCancel: () => void;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  category,
  onSave,
  onCancel
}) => {
  const [name, setName] = useState(category?.name || '');
  const [selectedColor, setSelectedColor] = useState(category?.color || CATEGORY_COLORS[0]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    setIsSubmitting(true);
    try {
      await onSave(name.trim(), selectedColor);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Name input group */}
      <div className="space-y-4">
        <label className="text-sm font-medium text-foreground">
          Category Name
        </label>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter category name..."
          className="w-full text-foreground"
          autoFocus
        />
      </div>

      {/* Color picker group */}
      <div className="space-y-4">
        <label className="text-sm font-medium text-foreground flex items-center gap-2">
          <Palette className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
          Color
        </label>
        <div className="flex flex-wrap gap-2">
          {CATEGORY_COLORS.map((color) => (
            <button
              key={color}
              type="button"
              onClick={() => setSelectedColor(color)}
              className={cn(
                "w-8 h-8 rounded-full border-2 transition-all hover:scale-110",
                selectedColor === color
                  ? "border-foreground shadow-md"
                  : "border-border hover:border-muted-foreground"
              )}
              style={{ backgroundColor: color }}
              title={color}
            />
          ))}
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex justify-end gap-4 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!name.trim() || isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              {category ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>
              <Check className="h-4 w-4 mr-2 hover:text-foreground/80 transition-colors" />
              {category ? 'Update Category' : 'Create Category'}
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export const CategoryManagerModal: React.FC<CategoryManagerModalProps> = ({
  isOpen,
  onClose,
  categories,
  onCreateCategory,
  onUpdateCategory,
  onDeleteCategory
}) => {
  const [editingCategory, setEditingCategory] = useState<SavedParagraphCategory | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<SavedParagraphCategory | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  const { getCategoryStats } = useCategoryManagement();

  // Handle create new category
  const handleCreateCategory = useCallback(async (name: string, color: string) => {
    const success = await onCreateCategory(name, color);
    if (success) {
      setShowCreateForm(false);
    }
  }, [onCreateCategory]);

  // Handle edit category
  const handleEditCategory = useCallback(async (name: string, color: string) => {
    if (!editingCategory) return;

    const success = await onUpdateCategory(editingCategory.id, { name, color });
    if (success) {
      setEditingCategory(null);
    }
  }, [editingCategory, onUpdateCategory]);

  // Handle delete category
  const handleDeleteCategory = useCallback(async () => {
    if (!deletingCategory) return;

    const success = await onDeleteCategory(deletingCategory.id, deletingCategory.name);
    if (success) {
      setDeletingCategory(null);
    }
  }, [deletingCategory, onDeleteCategory]);

  // Get category stats
  const getCategoryInfo = useCallback((category: SavedParagraphCategory) => {
    const stats = getCategoryStats(category.id);
    return {
      paragraphCount: stats.paragraphCount,
      isDefault: category.name.toLowerCase() === 'general'
    };
  }, [getCategoryStats]);

  // Handle modal close
  const handleClose = useCallback(() => {
    setEditingCategory(null);
    setShowCreateForm(false);
    setDeletingCategory(null);
    onClose();
  }, [onClose]);

  // Render main content
  const renderContent = () => {
    if (showCreateForm) {
      return (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <h3 className="font-medium text-foreground">Create New Category</h3>
          </div>
          <CategoryForm
            onSave={handleCreateCategory}
            onCancel={() => setShowCreateForm(false)}
          />
        </div>
      );
    }

    if (editingCategory) {
      return (
        <div className="space-y-8">
          <div className="flex items-center gap-2 mb-4">
            <h3 className="font-medium text-foreground">Edit Category</h3>
          </div>
          <CategoryForm
            category={editingCategory}
            onSave={handleEditCategory}
            onCancel={() => setEditingCategory(null)}
          />
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Create button */}
        <Button
          onClick={() => setShowCreateForm(true)}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2 hover:text-foreground/80 transition-colors" />
          Create New Category
        </Button>

        {/* Categories list */}
        {categories.length > 0 ? (
          <ScrollArea className="h-[400px] pr-3">
            <div className="space-y-2">
              {categories.map((category) => {
                const info = getCategoryInfo(category);
                return (
                  <CategoryItem
                    key={category.id}
                    category={category}
                    paragraphCount={info.paragraphCount}
                    isDefault={info.isDefault}
                    onEdit={setEditingCategory}
                    onDelete={setDeletingCategory}
                  />
                );
              })}
            </div>
          </ScrollArea>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-foreground hover:text-foreground/80 transition-colors mb-4" />
            <p className="text-muted-foreground mb-4">No categories found</p>
            <Button variant="outline" onClick={() => setShowCreateForm(true)}>
              <Plus className="h-4 w-4 mr-2 text-foreground hover:text-foreground/80 transition-colors" />
              Create Your First Category
            </Button>
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
        <DialogContent className="max-w-md max-h-[80vh] flex flex-col p-0 gap-0 overflow-hidden">
          <DialogHeader className="p-4 border-b border-border">
            <DialogTitle className="text-foreground">Manage Categories</DialogTitle>
          </DialogHeader>

          <div className="flex-1 p-4 overflow-hidden">
            {renderContent()}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete confirmation dialog */}
      <AlertDialog
        open={!!deletingCategory}
        onOpenChange={(open) => !open && setDeletingCategory(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-foreground">Delete Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the category "{deletingCategory?.name}"?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeletingCategory(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCategory}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
