import React, { useEffect, useCallback, useState, useMemo } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../../ui/dialog";
import { <PERSON><PERSON> } from "../../ui/button";
import { Input } from "../../ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../ui/alert-dialog";
import {
  Search,
  Plus,
  ArrowLeft,
  Settings,
  Loader2,
  AlertCircle,
  X,
  Save,
  Check,
  ChevronDown
} from 'lucide-react';
import { ManageSavedParagraphsModalProps } from './types';
import { useParagraphManagement } from './hooks/useParagraphManagement';
import { useCategoryManagement } from './hooks/useCategoryManagement';
import { usePendingInsertionActions } from '../../../stores/blockDocumentStore';

// Components
import { ParagraphsList } from './components/ParagraphsList';
import { ParagraphForm } from './components/ParagraphForm';
import { CategoryManagerModal } from './components/CategoryManagerModal';

export const ManageSavedParagraphsModalContainer: React.FC<ManageSavedParagraphsModalProps> = ({
  isOpen,
  onClose
}) => {
  // Ref for category dropdown
  const categoryDropdownRef = React.useRef<HTMLDivElement>(null);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [showNavigationConfirmation, setShowNavigationConfirmation] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<(() => void) | null>(null);
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [paragraphToDelete, setParagraphToDelete] = useState<{ id: string; title: string } | null>(null);
  // Local state for search query input value (not used for filtering)
  const [localSearchQuery, setLocalSearchQuery] = useState('');

  const {
    // State
    isLoading,
    error,
    currentView,
    searchQuery, // Add this to fix TypeScript errors
    displayedParagraphs,
    categories,
    selectedParagraph,

    // Actions
    initializeData,
    setSearchQuery: setSearchQueryFromHook,
    setSelectedCategory,
    clearError,
    navigateToList,
    navigateToCreate,
    navigateToEdit,
    createParagraph,
    updateParagraph,
    deleteParagraph
  } = useParagraphManagement();

  const {
    ensureDefaultCategory,
    createCategory,
    updateCategory,
    deleteCategory
  } = useCategoryManagement();

  // Hook for pending insertion content
  const { setPendingInsertionContent } = usePendingInsertionActions();

  // Check if any paragraphs have no category
  const hasUncategorizedParagraphs = useMemo(() => {
    return displayedParagraphs.some(p => !p.categoryId || p.categoryId === '');
  }, [displayedParagraphs]);

  // Filter paragraphs by selected categories
  const filteredParagraphs = useMemo(() => {
    if (selectedCategoryIds.length === 0) {
      return displayedParagraphs;
    }
    
    const specialNoCategory = '__no_category__';
    const hasNoCategoryFilter = selectedCategoryIds.includes(specialNoCategory);
    
    return displayedParagraphs.filter(paragraph => {
      // Handle special case for paragraphs with no category
      if (hasNoCategoryFilter && (!paragraph.categoryId || paragraph.categoryId === '')) {
        return true;
      }
      // Handle normal category filtering
      if (selectedCategoryIds.includes(paragraph.categoryId)) {
        return true;
      }
      
      return false;
    });
  }, [displayedParagraphs, selectedCategoryIds]);

  // Handle clicks outside of the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setIsCategoryDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize data when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('[ManageSavedParagraphsModal] Modal opened, initializing data...');
      // Always navigate to list view when modal opens
      navigateToList();
      initializeData().then(() => {
        console.log('[ManageSavedParagraphsModal] Data initialized.');
        // Note: We removed the automatic ensureDefaultCategory call
        // to prevent creating categories when the modal opens
      }).catch(err => {
        console.error('[ManageSavedParagraphsModal] Error initializing data:', err);
      });
    }
  }, [isOpen, navigateToList, initializeData]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearchQuery(value); // Update local state for the input field
    setSearchQueryFromHook(value); // Update store state for filtering
  }, [setSearchQueryFromHook]);

  const handleClearSearch = useCallback(() => {
    setLocalSearchQuery(''); // Clear local state
    setSearchQueryFromHook(''); // Clear store state
  }, [setSearchQueryFromHook]);

  const handleCategoryChange = useCallback((categoryId: string | null) => {
    setSelectedCategory(categoryId);
  }, [setSelectedCategory]);

  const handleCategoryFilterToggle = useCallback((categoryId: string) => {
    setSelectedCategoryIds(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  }, []);
  
  // Special filter for uncategorized (null) paragraphs
  const handleToggleNoCategoryFilter = useCallback(() => {
    setSelectedCategoryIds(prev => {
      const specialNoCategory = '__no_category__';
      if (prev.includes(specialNoCategory)) {
        return prev.filter(id => id !== specialNoCategory);
      } else {
        return [...prev, specialNoCategory];
      }
    });
  }, []);

  const handleClearCategoryFilter = useCallback(() => {
    setSelectedCategoryIds([]);
  }, []);

  const handleEditParagraph = useCallback((paragraphId: string) => {
    navigateToEdit(paragraphId);
  }, [navigateToEdit]);

  const handleDeleteParagraph = useCallback(async (paragraphId: string, title: string) => {
    setParagraphToDelete({ id: paragraphId, title });
    setIsDeleteConfirmOpen(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (paragraphToDelete) {
      await deleteParagraph(paragraphToDelete.id, paragraphToDelete.title);
      setParagraphToDelete(null);
      setIsDeleteConfirmOpen(false);
    }
  }, [paragraphToDelete, deleteParagraph]);

  const handleInsertParagraph = useCallback(async (paragraphId: string) => {
    try {
      console.log('Insert paragraph:', paragraphId);

      // Fetch the full content of the paragraph
      const result = await window.fileStorage.getSavedParagraphById(paragraphId);

      if (result && result.content) {
        // Set the pending insertion content in the store
        setPendingInsertionContent(result.content);
        console.log('✅ Paragraph content set for insertion:', result.content);
      } else {
        console.error('❌ Failed to fetch paragraph content or content is empty');
      }

      // Close the modal
      handleClose();
    } catch (error) {
      console.error('❌ Error inserting paragraph:', error);
    }
  }, [setPendingInsertionContent, handleClose]);

  const handleFormSubmit = useCallback(async (data: {
    title: string;
    categoryId: string;
    content: any; // Assuming content can be complex, adjust if specific type is known
    description?: string;
    tags?: string[];
  }) => {
    let success = false;
    if (currentView === 'create') {
      success = await createParagraph(
        data.title,
        data.categoryId,
        data.content,
        data.description,
        data.tags
      );
    } else if (currentView === 'edit' && selectedParagraph) {
      success = await updateParagraph(
        selectedParagraph.id,
        {
          title: data.title,
          categoryId: data.categoryId,
          description: data.description,
          tags: data.tags
        },
        data.content
      );
    }
    if (success) {
      navigateToList();
    }
  }, [currentView, selectedParagraph, createParagraph, updateParagraph, navigateToList]);

  const renderHeader = () => {
    switch (currentView) {
      case 'create':
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={navigateToList} className="h-8 w-8 bg-transparent hover:bg-transparent">
              <ArrowLeft className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
            </Button>
            <DialogTitle className="text-foreground">Create New Paragraph</DialogTitle>
          </div>
        );
      case 'edit':
        return (
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={navigateToList} className="h-8 w-8 bg-transparent hover:bg-transparent">
              <ArrowLeft className="h-4 w-4 text-foreground hover:text-foreground/80" />
            </Button>
            <DialogTitle className="text-foreground">Edit Paragraph</DialogTitle>
          </div>
        );
      default: // 'list' view
        return (
          <div className="flex items-center justify-between w-full">
            <DialogTitle className="text-foreground">Saved Paragraphs</DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsCategoryModalOpen(true)}
                className="h-8 w-8 bg-transparent hover:bg-transparent"
                title="Manage Categories"
              >
                <Settings className="h-4 w-4 text-foreground hover:text-foreground/80 transition-colors" />
              </Button>
              <Button variant="outline" size="sm" onClick={navigateToCreate} className="h-8">
                <Plus className="h-4 w-4 mr-1 hover:text-foreground/80" />
                New
              </Button>
            </div>
          </div>
        );
    }
  };

  const renderContent = () => {

    if (currentView === 'create' || currentView === 'edit') {
      return (
        <ParagraphForm
          mode={currentView}
          categories={categories}
          initialData={currentView === 'edit' ? selectedParagraph : undefined}
          onSubmit={handleFormSubmit}
          onCancel={navigateToList}
          onOpenCategoryModal={() => setIsCategoryModalOpen(true)}
        />
      );
    }

    // List View Specifics - Always show search bar
    return (
      <div className="space-y-4 flex flex-col flex-grow min-h-0">
        <div className="flex items-center gap-2">
          <div className="relative flex-grow">
            <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search paragraphs..."
              value={localSearchQuery}
              onChange={handleSearchChange}
              className="pl-9 pr-9 w-full bg-background text-foreground [&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::placeholder]:text-muted-foreground"
            />
            <X
              className="absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-foreground cursor-pointer hover:text-foreground/80 transition-colors"
              onClick={handleClearSearch}
            />
          </div>
          
          {/* Category Filter Dropdown */}
          <div className="relative min-w-[180px]" ref={categoryDropdownRef}>
            <div 
              className="flex items-center gap-2 p-2 border border-input rounded-md bg-background cursor-pointer h-10"
              onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
            >
              <span className="text-sm truncate text-foreground">
                {selectedCategoryIds.length === 0 
                  ? 'All Categories' 
                  : `${selectedCategoryIds.length} ${selectedCategoryIds.length === 1 ? 'Category' : 'Categories'}`}
              </span>
              <ChevronDown className="h-4 w-4 ml-auto text-foreground" />
            </div>
            
            <div 
              className={`absolute z-50 w-full mt-1 rounded-md border border-input bg-popover shadow-md max-h-[200px] overflow-y-auto custom-scrollbar ${isCategoryDropdownOpen ? '' : 'hidden'}`}
            >
              <div className="p-1">
                <div 
                  className="py-1.5 px-2 text-sm cursor-pointer rounded-sm hover:bg-muted/50 focus:bg-muted/50 flex items-center justify-between"
                  onClick={handleClearCategoryFilter}
                >
                  <span className="text-foreground">All Categories</span>
                  {selectedCategoryIds.length === 0 && <Check className="h-4 w-4 text-foreground" />}
                </div>
                
                {/* No Category option */}
                <div 
                  className="py-1.5 px-2 text-sm cursor-pointer rounded-sm hover:bg-muted/50 focus:bg-muted/50 flex items-center justify-between"
                  onClick={handleToggleNoCategoryFilter}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-3 h-3 rounded-full border border-border/30 bg-gray-400"
                    />
                    <span className="text-foreground">No Category</span>
                  </div>
                  {selectedCategoryIds.includes('__no_category__') && <Check className="h-4 w-4 text-foreground" />}
                </div>
                
                {categories.map(category => (
                  <div 
                    key={category.id}
                    className="py-1.5 px-2 text-sm cursor-pointer rounded-sm hover:bg-muted/50 focus:bg-muted/50 flex items-center justify-between"
                    onClick={() => handleCategoryFilterToggle(category.id)}
                  >
                    <div className="flex items-center gap-3">
                      {/* Color indicator */}
                      <div
                        className="w-3 h-3 rounded-full border border-border/30"
                        style={{ backgroundColor: category.color || '#6B8DD6' }}
                      />
                      <span className="text-foreground">{category.name}</span>
                    </div>
                    {selectedCategoryIds.includes(category.id) && <Check className="h-4 w-4 text-foreground" />}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
            <Loader2 className="h-8 w-8 animate-spin mb-2" />
            <p>Loading paragraphs...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-64 text-destructive">
            <AlertCircle className="h-8 w-8 mb-2" />
            <p>{error}</p>
            <Button variant="link" onClick={clearError} className="mt-2 text-destructive hover:text-destructive/80">
              Try again
            </Button>
          </div>
        ) : !localSearchQuery && filteredParagraphs.length === 0 && selectedCategoryIds.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full flex-grow text-center p-6">
            <p className="text-lg font-medium text-foreground mb-2">
              No Saved Paragraphs Yet
            </p>
            <p className="text-sm text-muted-foreground mb-4 max-w-sm">
              I don't see any saved paragraphs. Create your first one by clicking the 'New' button.
            </p>
            <Button onClick={navigateToCreate} size="sm" className="mb-6">
              <Plus className="h-4 w-4 mr-2 hover:text-foreground/80 transition-colors" />
              Create New Paragraph
            </Button>
            <p className="text-xs text-muted-foreground max-w-md">
              <strong>Tip:</strong> You can also create a saved paragraph by highlighting text within the text editor and clicking the button on the far right to save as a new saved paragraph.
            </p>
          </div>
        ) : (localSearchQuery || selectedCategoryIds.length > 0) && filteredParagraphs.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full flex-grow text-center p-6">
            <Search className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium text-foreground mb-2">
              No Results Found
            </p>
            {selectedCategoryIds.includes('__no_category__') ? (
              <>
                <p className="text-sm text-muted-foreground">
                  There are no paragraphs without a category.
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Try creating a new paragraph without assigning a category.
                </p>
              </>
            ) : localSearchQuery ? (
              <>
                <p className="text-sm text-muted-foreground">
                  Your search for "{localSearchQuery}" did not match any paragraphs.
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Try a different search term or clear the search.
                </p>
              </>
            ) : (
              <>
                <p className="text-sm text-muted-foreground">
                  No paragraphs found with the selected category filters.
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Try selecting different categories or click "All Categories" to see all paragraphs.
                </p>
              </>
            )}
          </div>
        ) : (
          <ParagraphsList
            paragraphs={filteredParagraphs}
            categories={categories}
            searchQuery={localSearchQuery}
            onEdit={handleEditParagraph}
            onDelete={handleDeleteParagraph}
            onInsert={handleInsertParagraph}
            onCreateNew={navigateToCreate}
          />
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-[86vw] h-[90vh] flex flex-col p-4 gap-0" showCloseButton={false}>
        <DialogHeader className="p-4 border-b border-border">
          {renderHeader()}
        </DialogHeader>

        <div className="flex-grow overflow-y-auto px-3 py-4 custom-scrollbar">
          {renderContent()}
        </div>

        <CategoryManagerModal
          isOpen={isCategoryModalOpen}
          onClose={() => setIsCategoryModalOpen(false)}
          categories={categories}
          onCreateCategory={createCategory}
          onUpdateCategory={updateCategory}
          onDeleteCategory={deleteCategory}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="text-foreground">Delete Paragraph</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the paragraph "{paragraphToDelete?.title}"?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => {
                setParagraphToDelete(null);
                setIsDeleteConfirmOpen(false);
              }}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DialogContent>
    </Dialog>
  );
};
