import React from 'react';
import { X } from 'lucide-react';
import { ModalProps } from '../types';

// Modal component for dialogs - Extracted as a separate component
export const Modal = React.memo(({
  isOpen,
  onClose,
  title,
  children,
  footer
}: ModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" onClick={onClose}>
      <div className="bg-background rounded-lg shadow-xl border border-border max-w-lg w-full p-6" onClick={e => e.stopPropagation()}>
        <div className="flex justify-between items-start mb-6">
          <h3 className="text-xl font-semibold text-foreground leading-none">{title}</h3>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors p-1 -mt-1 -mr-1 rounded-md hover:bg-muted"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="mb-6">
          {children}
        </div>
        <div className="flex justify-end gap-3">
          {footer}
        </div>
      </div>
    </div>
  );
});

Modal.displayName = 'Modal';
